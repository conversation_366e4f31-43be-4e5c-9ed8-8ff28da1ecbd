<?php $__env->startSection('content'); ?>
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card card-primary">
                <div class="card-header">
                    <h3 class="card-title"><PERSON><PERSON></h3>
                </div>
                <div class="card-body">
                    <form id="customer-search-form" method="GET" action="" class="mb-3 d-flex" style="gap: 8px;">
                        <div class="input-group">
                            <input type="text" id="customer-search-input" name="q" class="form-control" placeholder="TC ya da PlusCard ile arama">
                            <button class="btn btn-outline-secondary" type="submit">Ara</button>
                        </div>
                    </form>
                    <form action="<?php echo e(route('customers.store')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        
                        <!-- <PERSON><PERSON> Bilgileri -->
                        <h5 class="mb-3 text-primary">Kimlik Bilgileri</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="tc" class="form-label">TC Kimlik No</label>
                                    <input type="text" class="form-control" id="tc" name="tc" value="<?php echo e(old('tc', $prefill['tc'] ?? '')); ?>" maxlength="11" minlength="11" pattern="\d{11}" inputmode="numeric" placeholder="11 haneli TC Kimlik No">
                                    <small class="form-text text-muted">11 haneli TC Kimlik Numarası giriniz.</small>
                                    <?php $__errorArgs = ['tc'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="pluscard_no" class="form-label">Pluscard No</label>
                                    <input type="text" class="form-control" id="pluscard_no" name="pluscard_no" value="<?php echo e(old('pluscard_no', $prefill['pluscard_no'] ?? '')); ?>" placeholder="Pluscard numarası">
                                    <?php $__errorArgs = ['pluscard_no'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>
                        

                        <!-- Firma Bilgileri -->
                        <h5 class="mb-3 text-primary mt-4">Firma Bilgileri</h5>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="company_name" class="form-label">Firma Adı <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="company_name" name="company_name" value="<?php echo e(old('company_name', $prefill['company_name'] ?? '')); ?>" required placeholder="Firma adı">
                                    <?php $__errorArgs = ['company_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Yetkili Kişi Bilgileri -->
                        <h5 class="mb-3 text-primary mt-4">Yetkili Kişi Bilgileri</h5>

                        <!-- Eski tek yetkili kişi alanları (geriye dönük uyumluluk için) -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="authorized_title" class="form-label">Yetkili Ünvan</label>
                                    <input type="text" class="form-control" id="authorized_title" name="authorized_title" value="<?php echo e(old('authorized_title', $prefill['authorized_title'] ?? '')); ?>" placeholder="Örn: Genel Müdür, Satış Müdürü">
                                    <?php $__errorArgs = ['authorized_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="authorized_phone" class="form-label">Yetkili Telefon</label>
                                    <input type="text" class="form-control inputmask" id="authorized_phone" name="authorized_phone" value="<?php echo e(old('authorized_phone', $prefill['authorized_phone'] ?? '')); ?>" data-inputmask="'mask': '+99 999 999 99 99'" inputmode="tel" pattern="\+[0-9 ]{13,16}" placeholder="+90 5xx xxx xx xx">
                                    <?php $__errorArgs = ['authorized_phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="authorized_first_name" class="form-label">Yetkili İsim</label>
                                    <input type="text" class="form-control" id="authorized_first_name" name="authorized_first_name" value="<?php echo e(old('authorized_first_name', $prefill['authorized_first_name'] ?? '')); ?>" placeholder="Ad">
                                    <?php $__errorArgs = ['authorized_first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="authorized_last_name" class="form-label">Yetkili Soyisim</label>
                                    <input type="text" class="form-control" id="authorized_last_name" name="authorized_last_name" value="<?php echo e(old('authorized_last_name', $prefill['authorized_last_name'] ?? '')); ?>" placeholder="Soyad">
                                    <?php $__errorArgs = ['authorized_last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Yeni çoklu yetkili kişi sistemi -->
                        <h6 class="mb-3 text-secondary mt-4">Ek Yetkili Kişiler</h6>
                        <div class="mb-3">
                            <button type="button" id="add-authorized-person" class="btn btn-sm btn-success">
                                <i class="fas fa-plus"></i> Yeni Yetkili Ekle
                            </button>
                        </div>

                        <div id="authorized-persons-container">
                            <!-- Dinamik yetkili kişi alanları buraya eklenecek -->
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="email" class="form-label">E-posta <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="email" name="email" value="<?php echo e(old('email', $prefill['email'] ?? '')); ?>" required placeholder="<EMAIL>">
                                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>
                        <!-- İletişim Bilgileri -->
                        <h5 class="mb-3 text-primary mt-4">İletişim Bilgileri</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="phone_1" class="form-label">Şirket Telefon 1</label>
                                    <input type="text" class="form-control inputmask" id="phone_1" name="phone_1" value="<?php echo e(old('phone_1', $prefill['phone_1'] ?? '')); ?>" data-inputmask="'mask': '+99 999 999 99 99'" inputmode="tel" pattern="\+[0-9 ]{13,16}" placeholder="+90 2xx xxx xx xx">
                                    <?php $__errorArgs = ['phone_1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="phone_2" class="form-label">Şirket Telefon 2</label>
                                    <input type="text" class="form-control inputmask" id="phone_2" name="phone_2" value="<?php echo e(old('phone_2', $prefill['phone_2'] ?? '')); ?>" data-inputmask="'mask': '+99 999 999 99 99'" inputmode="tel" pattern="\+[0-9 ]{13,16}" placeholder="+90 2xx xxx xx xx">
                                    <?php $__errorArgs = ['phone_2'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="phone_3" class="form-label">Şirket Telefon 3</label>
                                    <input type="text" class="form-control inputmask" id="phone_3" name="phone_3" value="<?php echo e(old('phone_3', $prefill['phone_3'] ?? '')); ?>" data-inputmask="'mask': '+99 999 999 99 99'" inputmode="tel" pattern="\+[0-9 ]{13,16}" placeholder="+90 2xx xxx xx xx">
                                    <?php $__errorArgs = ['phone_3'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Adres Bilgileri -->
                        <h5 class="mb-3 text-primary mt-4">Adres Bilgileri</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="city" class="form-label">İl <span class="text-danger">*</span></label>
                                    <select class="form-control" id="city" name="city" required>
                                        <option value="">İl seçiniz</option>
                                    </select>
                                    <?php $__errorArgs = ['city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="district" class="form-label">İlçe <span class="text-danger">*</span></label>
                                    <select class="form-control" id="district" name="district" required disabled>
                                        <option value="">İlçe seçiniz</option>
                                    </select>
                                    <?php $__errorArgs = ['district'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="address" class="form-label">Adres <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="address" name="address" rows="3" required placeholder="Detaylı adres bilgisi"><?php echo e(old('address', $prefill['address'] ?? '')); ?></textarea>
                                    <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer d-flex flex-column gap-2">
                            <button type="submit" class="btn btn-success w-100">Kaydet</button>
                            <a href="<?php echo e(route('customers.index')); ?>" class="btn btn-secondary w-100">İptal</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script src="<?php echo e(asset('assets')); ?>/plugins/inputmask/jquery.inputmask.bundle.js"></script>
    <script>
        // TC Kimlik No sadece rakam ve 11 hane
        document.addEventListener('DOMContentLoaded', function() {
            var tcInput = document.getElementById('tc');
            if(tcInput) {
                tcInput.addEventListener('input', function (e) {
                    this.value = this.value.replace(/[^0-9]/g, '').slice(0, 11);
                });
            }
        });
        // Türkiye il-ilçe verisini harici JSON'dan yükle
        let cityDistricts = {};
        $(document).ready(function(){
            // AJAX ile müşteri arama ve form doldurma
            $('#customer-search-form').on('submit', function(e) {
                e.preventDefault();
                let q = $('#customer-search-input').val();
                if (!q) return;
                $.ajax({
                    url: '<?php echo e(route('customers.registrySearch')); ?>',
                    method: 'GET',
                    data: { q: q },
                    success: function(data) {
                        $('#name').val(data.name);
                        $('#lastname').val(data.lastname);
                        $('#tc').val(data.tc);
                        $('#pluscard_no').val(data.pluscard_no);
                        $('#email').val(data.email);
                        $('#authorized_title').val(data.authorized_title);
                        $('#authorized_first_name').val(data.authorized_first_name);
                        $('#authorized_last_name').val(data.authorized_last_name);
                        $('#authorized_phone').val(data.authorized_phone);
                        $('#company_name').val(data.company_name);
                        $('#phone_1').val(data.phone_1);
                        $('#phone_2').val(data.phone_2);
                        $('#city').val(data.city).trigger('change');
                        setTimeout(function() {
                            $('#district').val(data.district);
                        }, 200);
                        $('#address').val(data.address);
                    },
                    error: function(xhr) {
                        alert('Kayıt bulunamadı');
                    }
                });
            });
            const citySelect = $('#city');
            const districtSelect = $('#district');
            fetch('<?php echo e(asset('assets')); ?>/turkiye-il-ilce.json')
                .then(response => response.json())
                .then(data => {
                    cityDistricts = data;
                    Object.keys(cityDistricts).forEach(function(city) {
                        citySelect.append(`<option value="${city}">${city}</option>`);
                    });
                    // Eğer eski değer varsa otomatik doldur
                    const oldCity = "<?php echo e(old('city')); ?>";
                    const oldDistrict = "<?php echo e(old('district')); ?>";
                    if (oldCity) {
                        citySelect.val(oldCity).trigger('change');
                        if (oldDistrict) {
                            setTimeout(function() {
                                districtSelect.val(oldDistrict);
                            }, 100);
                        }
                    }
                });
            // İl değişince ilçe selectbox'ını doldur
            citySelect.on('change', function() {
                const selectedCity = $(this).val();
                districtSelect.empty().append('<option value="">İlçe seçiniz</option>');
                if (selectedCity && cityDistricts[selectedCity]) {
                    cityDistricts[selectedCity].forEach(function(district) {
                        districtSelect.append(`<option value="${district}">${district}</option>`);
                    });
                    districtSelect.prop('disabled', false);
                } else {
                    districtSelect.prop('disabled', true);
                }
            });
            $(".inputmask").inputmask({
                mask: "+99 999 999 99 99",
                showMaskOnHover: false,
                showMaskOnFocus: true,
                clearIncomplete: true,
                definitions: {
                    '9': {
                        validator: "[0-9]",
                        cardinality: 1,
                        definitionSymbol: "9"
                    }
                },
                onBeforePaste: function (pastedValue, opts) {
                    return pastedValue.replace(/[^\d\+]/g, '');
                },
                onKeyDown: function(e, buffer, caretPos, opts) {
                    var key = e.key;
                    if (!/[0-9]/.test(key) && key.length === 1) {
                        e.preventDefault();
                    }
                }
            });

    // Yetkili kişi yönetimi
    let authorizedPersonIndex = 0;

    function updateRemoveButtons() {
        const items = $('.authorized-person-item');
        if (items.length <= 1) {
            $('.remove-authorized-person').hide();
        } else {
            $('.remove-authorized-person').show();
        }
    }

    function updatePersonNumbers() {
        $('.authorized-person-item').each(function(index) {
            $(this).find('h6').text('Yetkili Kişi #' + (index + 1));
        });
    }

    $('#add-authorized-person').on('click', function() {
        const container = $('#authorized-persons-container');
        const newItem = `
            <div class="authorized-person-item border p-3 mb-3 rounded" data-index="${authorizedPersonIndex}">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Yetkili Kişi #${authorizedPersonIndex + 1}</h6>
                    <button type="button" class="btn btn-sm btn-danger remove-authorized-person">
                        <i class="fas fa-trash"></i> Kaldır
                    </button>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Yetkili Ünvan</label>
                            <input type="text" class="form-control" name="authorized_persons[${authorizedPersonIndex}][title]" placeholder="Örn: Genel Müdür, Satış Müdürü">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Yetkili Telefon</label>
                            <input type="text" class="form-control inputmask" name="authorized_persons[${authorizedPersonIndex}][phone]" data-inputmask="'mask': '+99 999 999 99 99'" placeholder="+90 5xx xxx xx xx">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Yetkili İsim</label>
                            <input type="text" class="form-control" name="authorized_persons[${authorizedPersonIndex}][first_name]" placeholder="Ad">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Yetkili Soyisim</label>
                            <input type="text" class="form-control" name="authorized_persons[${authorizedPersonIndex}][last_name]" placeholder="Soyad">
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.append(newItem);

        // Yeni eklenen telefon alanına inputmask uygula
        container.find('.inputmask').last().inputmask({
            mask: "+99 999 999 99 99",
            showMaskOnHover: false,
            showMaskOnFocus: true,
            clearIncomplete: true
        });

        authorizedPersonIndex++;
        updateRemoveButtons();
    });

    $(document).on('click', '.remove-authorized-person', function() {
        $(this).closest('.authorized-person-item').remove();
        updateRemoveButtons();
        updatePersonNumbers();
    });

        });
    </script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.index', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/crm.umram.online/resources/views/customers/create.blade.php ENDPATH**/ ?>