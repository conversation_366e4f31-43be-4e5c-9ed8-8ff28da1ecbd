<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Customer extends Model
{
    use HasFactory;

    protected $fillable = [
        'email',
        'authorized_title',
        'authorized_first_name',
        'authorized_last_name',
        'authorized_phone',
        'company_name',
        'phone_1',
        'phone_2',
        'phone_3',
        'city',
        'district',
        'address',
        'tc',
        'pluscard_no'
    ];

    public function scopeSearch($query, $q)
    {
        if (!$q) return $query;
        $qNoSpace = str_replace(' ', '', $q);

        return $query->where(function($sub) use ($q, $qNoSpace) {
            $sub->where('authorized_first_name', 'ILIKE', "%$q%")
                ->orWhere('authorized_last_name', 'ILIKE', "%$q%")
                ->orWhere('email', 'ILIKE', "%$q%")
                ->orWhere('phone_1', 'ILIKE', "%$q%")
                ->orWhereRaw("REPLACE(phone_1, ' ', '') ILIKE ?", ["%$qNoSpace%"])
                ->orWhereRaw("REPLACE(phone_2, ' ', '') ILIKE ?", ["%$qNoSpace%"])
                ->orWhereRaw("REPLACE(phone_3, ' ', '') ILIKE ?", ["%$qNoSpace%"])
                ->orWhere('company_name', 'ILIKE', "%$q%")
                ->orWhere('city', 'ILIKE', "%$q%")
                ->orWhere('district', 'ILIKE', "%$q%")
                ->orWhereHas('authorizedPersons', function($authorizedQuery) use ($q, $qNoSpace) {
                    $authorizedQuery->where('first_name', 'ILIKE', "%$q%")
                        ->orWhere('last_name', 'ILIKE', "%$q%")
                        ->orWhere('title', 'ILIKE', "%$q%")
                        ->orWhere('phone', 'ILIKE', "%$q%")
                        ->orWhereRaw("REPLACE(phone, ' ', '') ILIKE ?", ["%$qNoSpace%"]);
                });
        });
    }

    public function customerFollowups()
    {
        return $this->hasMany(CustomerFollowup::class);
    }

    public function authorizedPersons()
    {
        return $this->hasMany(CustomerAuthorizedPerson::class);
    }
}